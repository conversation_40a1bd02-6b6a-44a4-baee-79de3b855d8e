import React from "react";
import Card_1 from "./Card_1";

const cards = [
  {
    title: "Machine Learning Model Development",
    description:
      "Valueans is a top machine learning business that provides full-service ML model creation. We assess your company's requirements, develop accurate algorithms, and train models using actual or simulated data.",
    bgColor: "bg-white",
  },
  {
    title: "Data Engineering",
    description:
      "Utilize our dependable <a href='/services/data-engineering-solutions' class='text-[#7716BC] hover:underline'>data engineering</a> services to streamline processes. We collect data from several sources, construct reliable data pipelines, and get it ready for analysis. Our safe and scalable solutions offer useful information for differentiating your business from the competition.",
    bgColor: "bg-blue-100",
  },
  {
    title: "Data Analysis",
    description:
      "Use machine learning to discover important things about market trends and consumer demands. To remain ahead of the competition, you may generate precise forecasts with the use of our sophisticated <a href='/services/data-analytics-services' class='text-[#7716BC] hover:underline'>data analytics</a> services. For effective solutions catered to your company's objectives, collaborate with Valueans.",
    bgColor: "bg-white",
  },
  {
    title: "Big Data Services",
    description:
      "With our ML services, you can store and analyze big data in real-time and extract advanced analytical insights from massive datasets with the aid of big data consultation, implementation, support, and big data as a service.",
    bgColor: "bg-blue-100",
  },
  {
    title: "Machine Learning as a Service",
    description:
      "We provide sophisticated data analytics insights using machine learning technology that help improve current machine learning projects without spending money on internal resources.",
    bgColor: "bg-white",
  },
  {
    title: "Data Science Services",
    description:
      "At Valueans, we advise, create, and assist businesses with data science solutions so they may conduct experiments on their data to get business insights.",
    bgColor: "bg-blue-100",
  },
  {
    title: "Data Mining Services",
    description:
      "You can obtain important insights from sizable, diverse, and dynamic data sets without having to hire in-house data mining experts.",
    bgColor: "bg-blue-100",
  },
];

const Section4 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
      Valueans <span className="text-[#F245A1]">Machine Learning</span> Services
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-5 md:mt-[42px]">
      {cards.map((card, idx) => (
        <Card_1
          key={idx}
          title={card.title}
          description={card.description}
          bgColor={card.bgColor}
        />
      ))}
    </div>
  </div>
);

export default Section4;
