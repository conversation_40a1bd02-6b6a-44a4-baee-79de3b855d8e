import React from "react";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";
import ServiceCount from "../Services/ServiceCount";

const steps = [
  {
    count: "1",
    title: "Research and Plan",
    items: [
      "In-depth exploration of the structured and unstructured data",
      "Perform proper research and analysis",
      "Step-by-step planning and strategizing of the process",
    ],
  },
  {
    count: "2",
    title: "Develop",
    items: [
      "Develop smart ML models",
      "Carry out training to test their efficiencies",
      "Perform iteration",
    ],
  },
  {
    count: "3",
    title: "Deploy",
    items: [
      "Perform necessary testing",
      "Make the code error and bug-free",
      "Ask for your feedback",
      "Deploy the final product onto the platform that best suits your software requirements.",
    ],
  },
  {
    count: "4",
    title: "Support and Maintenance",
    items: [
      "Provide <a href='/services/it-maintenance-support-services' class='text-[#7716BC] hover:underline'>continuous support and maintenance</a> post-deployment",
      "Make sure the application is always performing as per the standards",
    ],
  },
];

const Section6 = () => (
  <section className="bg-pink-100 py-8 my-24">
    <div className="container">
      <h2 className="text-2xl md:text-4xl text-center font-semibold mb-8">
        Our Innovative{" "}
        <span className="text-[#F245A1]">
          Machine Learning <br /> Development
        </span>{" "}
        Process
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        {steps.map((step) => (
          <div
            key={step.count}
            className="flex flex-row md:flex-col items-center justify-center gap-4"
          >
            <ServiceCount count={step.count} />
            <ServiceLifecycleCard title={step.title} items={step.items} />
          </div>
        ))}
      </div>
    </div>
  </section>
);

export default Section6;
