import React from "react";

const healthcareTech = [
  {
    title: "Internet of Medical Things",
    description:
      "Establish a network of interconnected health systems, software programs, and medical equipment to maximize the benefits of data-driven care and improve patient outcomes.",
  },
  {
    title: "AI and ML",
    description:
      "By using <a href='/services/ai-business-solutions' class='text-[#7716BC] hover:underline'>artificial intelligence</a> to supplement human capabilities in carrying out intricate medical-related activities, you may increase operational performance, treatment effectiveness, and diagnostic accuracy.",
  },
  {
    title: "Big data and analytics",
    description:
      "Find patient trends and correlations, and make sure that the results inform strategy and decision-making to save expenses, improve customer satisfaction, and make wiser treatment choices possible.",
  },
  {
    title: "Cloud",
    description:
      "Move your workloads to the cloud while adhering to security and compliance regulations to enhance system uptime and expedite the delivery of cutting-edge healthcare services.",
  },
  {
    title: "Blockchain",
    description:
      "Use blockchain technology, which offers transparency, data provenance, and trust, to transform the way important research and healthcare data are distributed to important parties.",
  },
  {
    title: "Cybersecurity",
    description:
      "By using the security-by-design method and doing routine risk assessments, you may safeguard private medical information and make sure that HIPAA and other healthcare confidentiality laws are followed.",
  },
];

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-sm p-4 rounded-md shadow-md border border-purple-500 bg-white">
      <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      <p
        className="text-sm md:text-base text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section5 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Our Key Areas of Expertise
      </h2>
      <div className="container mx-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {healthcareTech.map((card, index) => (
            <Card
              key={card.title} // Adding a key prop here
              title={card.title}
              description={card.description}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section5;
