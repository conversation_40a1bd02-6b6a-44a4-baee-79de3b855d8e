export async function generateMetadata({ params }) {
  // For dynamic routes, we'll use a generic canonical URL
  // In a real implementation, you might fetch the blog data to get the actual title
  return {
    title: `Blog Post - ${params.slug} | Valueans`,
    description: "Read our latest blog post about software development, technology trends, and business insights.",
    alternates: {
      canonical: `https://valueans.com/blog/${params.slug}`,
    },
     openGraph: {
    title: "Stay Informed of engaging Articles and Trends with Our Blog",
    description: "Dive into our blog to get insights into trends and solutions for custom software development. Stay tuned with our extensive research and creative solutions.",
    url: `https://valueans.com/blog/${params.slug}`,
    type: "website",
  },
  };
}

export default function BlogSlugLayout({ children }) {
  return children;
}
