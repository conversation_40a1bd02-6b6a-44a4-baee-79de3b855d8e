async function fetchBlogData(slug) {
  try {
    const response = await fetch("https://api.valueans.com/api/blogs/", {
      // Add cache revalidation for better performance
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Find the blog with matching slug
    const foundBlog = data.results?.find(blog => blog.slug_field === slug);

    return foundBlog;
  } catch (error) {
    console.error("Error fetching blog data for metadata:", error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const blog = await fetchBlogData(params.slug);

  // Fallback metadata if blog is not found
  if (!blog) {
    return {
      title: `Blog Post - ${params.slug} | Valueans`,
      description: "Read our latest blog post about software development, technology trends, and business insights.",
      alternates: {
        canonical: `https://valueans.com/blog/${params.slug}`,
      },
      openGraph: {
        title: `Blog Post - ${params.slug} | Valueans`,
        description: "Read our latest blog post about software development, technology trends, and business insights.",
        url: `https://valueans.com/blog/${params.slug}`,
        type: "article",
      },
    };
  }

  // Dynamic metadata based on blog data
  const title = `${blog.title}`;
  const description = blog.description_for_seo || blog.content?.replace(/<[^>]*>/g, '').substring(0, 160) + '...' || "Read our latest blog post about software development, technology trends, and business insights.";
  const canonicalUrl = `https://valueans.com/blog/${params.slug}`;
  const ogImage = blog.image || "https://valueans.com/Images/default-blog-og.png"; // Fallback image

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      type: "article",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ],
      publishedTime: blog.created_at,
      authors: [blog.uploaded_by?.name || 'Valueans'],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
  };
}

export default function BlogSlugLayout({ children }) {
  return children;
}
