async function fetchBlogData(slug) {
  try {
    const response = await fetch("https://api.valueans.com/api/blogs/", {
      // Add cache revalidation for better performance
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Find the blog with matching slug
    const foundBlog = data.results?.find(blog => blog.slug_field === slug);

    return foundBlog;
  } catch (error) {
    console.error("Error fetching blog data for metadata:", error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const blog = await fetchBlogData(params.slug);

  // Fallback metadata if blog is not found
  if (!blog) {
    const fallbackTitle = `Blog Post - ${params.slug} | Valueans`;
    const fallbackDescription = "Read our latest blog post about software development, technology trends, and business insights.";
    const fallbackUrl = `https://valueans.com/blog/${params.slug}`;

    return {
      title: fallbackTitle,
      description: fallbackDescription,
      alternates: {
        canonical: fallbackUrl,
      },
      openGraph: {
        title: fallbackTitle,
        description: fallbackDescription,
        url: fallbackUrl,
        type: "article",
        siteName: "Valueans",
        locale: "en_US",
        images: [
          {
            url: "https://valueans.com/Images/default-blog-og.png",
            width: 1200,
            height: 630,
            alt: "Valueans Blog",
            type: "image/png",
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: fallbackTitle,
        description: fallbackDescription,
        images: ["https://valueans.com/Images/default-blog-og.png"],
        site: "@valueans",
      },
    };
  }

  // Dynamic metadata based on blog data
  const title = `${blog.title}`;

  // Generate descriptions optimized for different platforms
  let metaDescription; // For standard meta description (160 chars max)
  let socialDescription; // For Open Graph/social media (300 chars max)

  if (blog.description_for_seo) {
    // Use the full description_for_seo for social media (no truncation)
    socialDescription = blog.description_for_seo.trim();
    // Truncate only for meta description if needed
    metaDescription = socialDescription.length > 160
      ? socialDescription.substring(0, 160) + '...'
      : socialDescription;
  } else if (blog.content) {
    // Strip HTML and create descriptions from content
    const strippedContent = blog.content.replace(/<[^>]*>/g, '').trim();
    // For social media: allow up to 300 characters
    socialDescription = strippedContent.length > 300
      ? strippedContent.substring(0, 300) + '...'
      : strippedContent;
    // For meta description: limit to 160 characters
    metaDescription = strippedContent.length > 160
      ? strippedContent.substring(0, 160) + '...'
      : strippedContent;
  } else {
    // Generic fallback for both
    const fallbackText = "Read our latest blog post about software development, technology trends, and business insights.";
    socialDescription = fallbackText;
    metaDescription = fallbackText;
  }

  const canonicalUrl = `https://valueans.com/blog/${params.slug}`;
  const ogImage = blog.image || "https://valueans.com/Images/default-blog-og.png"; // Fallback image

  return {
    title,
    description: metaDescription, // Use truncated description for meta tag
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description: socialDescription, // Use full description for social sharing
      url: canonicalUrl,
      type: "article",
      siteName: "Valueans", // Required for LinkedIn
      locale: "en_US", // Required for proper LinkedIn parsing
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: blog.title,
          type: "image/jpeg", // Explicit image type for better compatibility
        },
      ],
      publishedTime: blog.created_at,
      modifiedTime: blog.updated_at || blog.created_at, // LinkedIn prefers this
      authors: [blog.uploaded_by?.name || 'Valueans'],
      section: blog.categories?.name || 'Technology', // Article section for LinkedIn
    },
    twitter: {
      card: "summary_large_image",
      title,
      description: socialDescription, // Use full description for Twitter
      images: [ogImage],
      site: "@valueans", // Add Twitter handle if available
    },
    // Additional meta tags for better social media compatibility
    other: {
      // LinkedIn specific meta tags
      'article:author': blog.uploaded_by?.name || 'Valueans',
      'article:published_time': blog.created_at,
      'article:modified_time': blog.updated_at || blog.created_at,
      'article:section': blog.categories?.name || 'Technology',
      // Ensure proper encoding
      'charset': 'utf-8',
    },
  };
}

export default function BlogSlugLayout({ children }) {
  return children;
}
