import React from "react";

const Section8 = () => {
  return (
    <section className="bg-purple-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="container flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Why You Should Consider Getting{" "}
            <span className="text-[#F245A1]">
              Machine Learning Consultation
            </span>{" "}
            Right Now
          </h2>
          <p className="text-base md:text-xl">
            There are several advantages to implementing machine learning
            systems, such as:
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <div className="py-5 flex flex-col gap-3 md:gap-5 text-justify">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Increased worker productivity as a result of normal and
                  repetitive jobs being automated using <a href='/technologies/nlp-solutions' class='text-[#7716BC] hover:underline'> natural language
                  processing</a> and computer vision.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Improved customer service since chatbots and virtual
                  assistants driven by AI provide real-time contact.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Increased lead selection and opportunity insights, which
                  accelerated the sales process.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-xl">
                  Lower maintenance expenses for equipment as a result of
                  preventative maintenance and predictive monitoring.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg ">
                  Improved production efficiency as a result of improved
                  production process optimization, demand and throughput
                  forecasting, and product quality prediction modeling.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section8;
