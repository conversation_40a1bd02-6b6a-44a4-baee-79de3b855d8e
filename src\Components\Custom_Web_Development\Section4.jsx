import ServiceCard_2 from "../Card/ServiceCard_2";
const cardData = [
  {
    title: "Corporate Website Development & Design",
    content:
      "With an emphasis on distinctive, expert web design, we collaborate closely with enterprise-level clients to develop exceptional, safe, and high-performing bespoke web development software. Valueans has the resources and expertise to guarantee that your objectives are completely fulfilled.",
  },
  {
    title: "Drupal Design & Development",
    content:
      "Our expertise in developing and maintaining expert, cutting-edge, and highly customized Drupal-based solutions is unparalleled. Drupal has been a mainstay of our development, design, and consulting activities.",
  },
  {
    title: "Web Design & Development With HTML5",
    content:
      "With the help of Valuean’s HTML5 Web Development Services in the USA and other countries, you can create and implement a comprehensive HTML5 solution for your company that is responsive, scalable, and optimized for high performance with fast loading times. It can also manage any spikes in visitor traffic.",
  },
  {
    title: "Design & Development Using Laravel",
    content:
      "With Valueans's custom web development services, skilled Laravel developers can design and create web-based properties and/or apps that will satisfy your unique user, company, industry, and/or vertical demands using an agile development process.",
  },
  {
    title: "Custom Web Design",
    content:
      "Collaborate with leading web designers to create a unique website that is optimized for conversion and local search. We provide end-to-end web app development services to increase your exposure and engagement. We provide comprehensive strategy and planning, distinctive <a href='/services/ui-ux-design-services' class='text-[#7716BC] hover:underline'>  UX and UI design services</a>, SEO, and <a href='/services/quality-assurance-services' class='text-[#7716BC] hover:underline'> quality assurance.</a>",
  },
  {
    title: "Custom Website Redesign",
    content:
      "With the help of seasoned digital strategists and award-winning designers, evaluate and enhance the look of your website. We develop an on-brand solution that is a search engine, engagement, and conversion-optimized, whether it's a CMS migration or a total website makeover.",
  },
  {
    title: "Web Design For ECommerce",
    content:
      "To create an <a href='/industries/ecommerce-app-development' class='text-[#7716BC] hover:underline'>eCommerce web design</a>  that meets your needs, our professionals collaborate with all of the top eCommerce systems, such as Magento, Shopify, and WooCommerce. We provide a seamless shopping experience that increases sales for your company, generates leads, and lowers cart abandonment rates.",
  },
  {
    title: "Web Design For Business-To-Business",
    content:
      "With our web application development services, you can collaborate with our group to create a market-leading business-to-business website that will generate leads for your company. In order to reach your target market and establish new relationships, we will assist you in increasing your visibility, authority, and trust through expert and personalized design.",
  },
  {
    title: "Local Search Engine Optimization",
    content:
      "Do you want to become more visible in the marketplace? To create and carry out a unique search engine optimization strategy, collaborate with our strategists. Our experts will develop an SEO strategy based on information about your audience, rivals, and industry to improve your local search exposure and draw in new clients.",
  },
];

const Card = ({ title, description }) => {
  return (
    <div className="block bg-white w-full md:max-w-sm md:h-[288px] p-4 shadow-md rounded-xl overflow-hidden">
      <h3 className="text-lg md:text-2xl text-[#7716BC] capitalize font-semibold mb-2 md:mb-4">
        {title}
      </h3>
      <p
        className="text-sm md:text-base text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section4 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-3xl md:leading-[57px] text-center font-semibold">
        <span className="text-[#F245A1]">Valueans</span> End-to-End Web App
        Development Services
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4 mt-6 md:mt-[42px]">
        {cardData.map((card, index) => (
          <ServiceCard_2
            key={index}
            title={card.title}
            content={card.content}
          />
        ))}
      </div>
    </div>
  );
};

export default Section4;
