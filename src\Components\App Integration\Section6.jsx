import React from "react";
import Card from "./Card_2";

const Section6 = () => {
  const cardData = [
    {
      title: "Software Industry",
      content: [
        "Data Synchronization",
        "3rd Party Software Integration",
        "Cloud App Integration",
        "API Development",
      ],
    },
    {
      title: "Healthcare Industry",
      content: [
        "Telemedicine Web Integration",
        "EMR Integration",
        "Integration of Laboratory Systems",
        "Patient Portals",
      ],
    },
    {
      title: "Legal Industry",
      content: [
        "Compliance Management",
        "ERP Integration",
        "Data Migration",
        "Workflows Automation",
      ],
    },
    {
      title: "Retail Industry",
      content: [
        "POS Systems",
        "Supply Chain Integration",
        "Inventory Integration",
        "<a href='/industries/ecommerce-app-development' class='text-[#7716BC] hover:underline'>E-commerce Integration</a>",
      ],
    },
    {
      title: "Real Estate",
      content: [
        "Market Data Integration",
        "<a href='/industries/real-estate-software-development' class='text-[#7716BC] hover:underline'>Real estate IT services</a>",
        "Investment Tools Integration",
        "Tenant Portal Integration",
        "Integration of Property Management Software",
      ],
    },
    {
      title: "Manufacturing Industry",
      content: [
        "Logistics Integration",
        "MES Integration",
        "Quality Control Monitoring",
        "Sensor Integration through <a href='/technologies/iot-deployment-technologies' class='text-[#7716BC] hover:underline'> IOT deployment services</a>",
        "Robotics Systems",
      ],
    },
  ];

  return (
    <div className="container">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        We’ve Delivered Seamless{" "}
        <span className="text-[#F245A1]">App to App Integration Services</span>{" "}
        for Diverse Industries
      </h2>
      <div className="container p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-center gap-6">
          {cardData.map((card, index) => (
            <Card
              key={index}
              title={card.title}
              content={card.content}
              className="h-full"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section6;
