"use client";
import React from "react";
import Link from "next/link";

const Services = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="container mx-auto p-10 border rounded-lg">
      <div className="grid grid-cols-6 gap-4 ">
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold"> Custom Software Solutions</h4>
            <Link
              href="/services/enterprise-software-development"
              onClick={(e) => handleEnhancedNavigation("/services/enterprise-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/enterprise-software-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Software Development
            </Link>
            <Link
              href="/services/custom-web-development-services"
              onClick={(e) => handleEnhancedNavigation("/services/custom-web-development-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/custom-web-development-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Custom Web Development
            </Link>
            <Link
              href="/services/saas-application-development"
              onClick={(e) => handleEnhancedNavigation("/services/saas-application-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/saas-application-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Saas Development
            </Link>
            <Link
              href="/services/full-stack-development-services"
              onClick={(e) => handleEnhancedNavigation("/services/full-stack-development-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/full-stack-development-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Full Stack Development
            </Link>
            <Link
              href="/services/enterprise-mobile-app-development"
              onClick={(e) => handleEnhancedNavigation("/services/enterprise-mobile-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/enterprise-mobile-app-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Mobile App Development
            </Link>
            <Link
              href="/services/fintech-software-development"
              onClick={(e) => handleEnhancedNavigation("/services/fintech-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/fintech-software-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Fintech
            </Link>
            <Link
              href="/services/healthcare-software-development"
              onClick={(e) => handleEnhancedNavigation("/services/healthcare-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/healthcare-software-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Healthcare Development
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">IT Consulting</h4>
            <Link
              href="/services/ai-business-solutions"
              onClick={(e) => handleEnhancedNavigation("/services/ai-business-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/ai-business-solutions", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              AI
            </Link>
            <Link
              href="/services/enterprise-application-integration-services"
              onClick={(e) => handleEnhancedNavigation("/services/enterprise-application-integration-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/enterprise-application-integration-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Application Integration
            </Link>
            <Link
              href="/services/managed-cloud-services"
              onClick={(e) => handleEnhancedNavigation("/services/managed-cloud-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/managed-cloud-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Cloud Services
            </Link>
            <Link
              href="/services/business-intelligence-analytics-services"
              onClick={(e) => handleEnhancedNavigation("/services/business-intelligence-analytics-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/business-intelligence-analytics-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Business Intelligence
            </Link>
            <Link
              href="/services/product-management-consulting"
              onClick={(e) => handleEnhancedNavigation("/services/product-management-consulting", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/product-management-consulting", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Product Management
            </Link>
            <Link
              href="/services/machine-learning-services"
              onClick={(e) => handleEnhancedNavigation("/services/machine-learning-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/machine-learning-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              ML
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Testing & QA</h4>
            <Link
              href="/services/quality-assurance-services"
              onClick={(e) => handleEnhancedNavigation("/services/quality-assurance-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/quality-assurance-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Quality Assurance & Testing
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Data Solutions</h4>
            <Link
              href="/services/data-analytics-services"
              onClick={(e) => handleEnhancedNavigation("/services/data-analytics-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/data-analytics-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Analytics
            </Link>
            <Link
              href="/services/data-engineering-solutions"
              onClick={(e) => handleEnhancedNavigation("/services/data-engineering-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/data-engineering-solutions", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Engineering
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Design Services</h4>
            <Link
              href="/services/ui-ux-design-services"
              onClick={(e) => handleEnhancedNavigation("/services/ui-ux-design-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/ui-ux-design-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              UI UX designing
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Application Services</h4>
            <Link
              href="/services/it-maintenance-support-services"
              onClick={(e) => handleEnhancedNavigation("/services/it-maintenance-support-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/it-maintenance-support-services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Maintainence and Support
            </Link>
            <Link
              href="/services/dedicated-development-team"
              onClick={(e) => handleEnhancedNavigation("/services/dedicated-development-team", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/services/dedicated-development-team", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Dedicated Deployment Teams
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
