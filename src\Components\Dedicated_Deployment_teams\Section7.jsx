import Card_2 from "../App Integration/Card";

// const Card_1 = ({ title, description }) => {
//   return (
//     <div className="block w-full md:w-[317px] h-auto md:h-[400px] bg-white border border-purple-500 p-4 shadow-sm rounded-md">
//       <h3 className="text-lg md:text-2xl font-semibold mb-3 md:mb-4">{title}</h3>
//       <p className="text-base md:text-xl">{description}</p>
//     </div>
//   );
// };
// const Card_2 = ({ title, description }) => {
//   return (
//     <div className="block w-full md:w-[307px] h-auto md:h-[320px] bg-white border border-purple-500 p-4 shadow-sm rounded-md">
//       <h3 className="text-lg md:text-2xl font-semibold mb-3 md:mb-4">{title}</h3>
//       <p className="text-base md:text-xl">{description}</p>
//     </div>
//   );
// };
// const Card_3 = ({ title, description }) => {
//   return (
//     <div className="block w-full md:w-[307px] h-auto md:h-[280px] bg-white border border-purple-500 p-4 shadow-sm rounded-md">
//       <h3 className="text-lg md:text-2xl font-semibold mb-3 md:mb-4">{title}</h3>
//       <p className="text-base md:text-xl">{description}</p>
//     </div>
//   );
// };
           
const Section7 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Hire <span className="text-[#F245A1]">Dedicated Developers</span> from
        Valueans
      </h2>
      <p className="text-base text-center md:text-xl">
        In some circumstances, selecting a specialized development team might be
        a wise business decision. Let's briefly go over several situations in
        which this could be the best choice for you. 
      </p>
      <div className="flex flex-col md:flex-row justify-center items-center gap-4 mt-6 md:mt-[42px]">
        <Card_2
          title={"Formation of a Dedicated Team"}
          description={
            "We carefully choose IT personnel based on your unique requirements, making sure they have the necessary abilities and credentials.<a href='/services/full-stack-development-services' class='text-[#7716BC] hover:underline'> Frontend and backend engineers</a>,<a href='/services/ui-ux-design-services' class='text-[#7716BC] hover:underline'> UX/UI designers</a>, QAs, DevOps, and project managers are among the people in our skill pool."
          }
          height={"md:h-[400px]"}
        />
        <Card_2
          title={"Project Integration"}
          description={
            "You are in charge of the development process and provide the project requirements. After that, our staff seamlessly becomes part of your project, making sure that everything fits in with your strategic objectives."
          }
          height={"md:h-[400px]"}
        />
        <Card_2
          title={"Continuous Assistance"}
          description={
            "We take care of all administrative duties, employee welfare, and internal training to make sure the team is properly run and prepared for optimal performance."
          }
          height={"md:h-[400px]"}
        />
        <Card_2
          title={"Flexible Project Scalability"}
          description={
            "We adjust to the changing demands of your project. At every stage, we adapt the size and ability of your team to ensure responsiveness and agility."
          }
          height={"md:h-[400px]"}
        />
      </div>
    </div>
  );
};

export default Section7;
