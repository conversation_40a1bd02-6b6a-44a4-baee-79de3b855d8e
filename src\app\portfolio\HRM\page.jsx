import React from "react";
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "UI/UX for Employee Management & Attendance | Valueans",
  description: "Valueans designed a user-friendly UI/UX for its in-house HRM platform, enabling attendance tracking, performance metrics, and internal communication.",
  alternates: {
    canonical: "https://valueans.com/portfolio/HRM",
  },
  openGraph: {
    title: "UI/UX for Employee Management & Attendance | Valueans",
    description: "Valueans designed a user-friendly UI/UX for its in-house HRM platform, enabling attendance tracking, performance metrics, and internal communication.",
    url: "https://valueans.com/portfolio/HRM",
    type: "website",
  },
};
const page = () => {
  const images = ["/Images/HRM2.png", "/Images/HRM3.png", "/Images/HRM4.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/HRm-bg.png"} />
      <Section2
        image={"/Images/HRM1.png"}
        heading={"HRM"}
        paragraph1={
          "Our team developed the UI/UX of an HRM deployment system for Valueans to help the organization streamline its Human Resources activities. The platform was originally developed to help save the time that was required to carry out everyday activities related to the HR department.We developed an easy interface for employees and management so they can easily navigate through different functions."
        }
        paragraph2={
          "Employees can mark their daily attendance using this platform and can see statistics on the dashboard. Moreover, it tracks their daily working hours, which is helpful while evaluating their performance. Employees and management can also look at the performance metrics, which fosters transparency within the organization. Therefore, we had to develop dashboards where they can access the information."
        }
        rowReverse={"md:flex-row-reverse"}
      />
      <Section4
        paragraph1={
          "Moreover, all the important notices can be easily received by employees on this platform, which eliminates the chances of miscommunication. All the team members can connect in one place and have clear communication. Therefore, we ensured that the information was presented clearly to the employees and management with captivating UI/UX elements."
        }
      />
      <Section5 images={images} />
    </div>
  );
};

export default page;
