import React from "react";
import Card from "./Card";
import ServiceCount from "../Services/ServiceCount_2";

const Process = () => {
  return (
    <div className="bg-pink-100 py-10 my-10 md:my-24">
      <div className="container">
        <h2 className="text-xl md:text-3xl md:leading-[40px] font-bold text-center">
          Our Complete{" "}
          <span className="text-[#7716BC]">
            Maintenance and Support Process
          </span>
        </h2>
        <p className="text-base md:text-xl text-center">
         At Valueans, our <a href='/services/it-maintenance-support-services' class='text-[#7716BC] hover:underline'> maintenance and support</a> services are all about keeping your business to perform best while keeping your businesses running smoothly. We take a complete hand on and initiative-taking approach to catch and fix issues early, we keep your system up to date and make sure that everything is working efficiently. Step by step, we focus on what matters most to your business, so you can stay tension free and focused on your goals and success. <br />{" "}
          <span className="font-semibold">Here is how we achieve this:</span>
        </p>

        <div className="flex flex-col md:flex-row justify-center items-center">
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>1</ServiceCount>
            <Card
              title={"Requirement Analysis"}
              description={
                "We start by understanding your business and how developers do their work. By learning about your processes, we can set the right maintenance goals that match your business needs. This ensures that our plans are about to support your team and help your operations run smoothly."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>2</ServiceCount>
            <Card
              title={"Program Evaluation"}
              description={
                "We take a close look at your application, by understanding how complex they are and knowing what can be the possible challenges, during this step, we also share knowledge to make sure that we fully understand how your system is set up and how everything works together."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>3</ServiceCount>
            <Card
              title={"Maintenance Plan development"}
              description={
                "Based on what we learnt from evaluation, we put together a maintenance plan just for you. This plan is for your specific needs, regular updates, fixing issues, and keeping an eye on your system. Once it is ready, we will share it with you for approval."
              }
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center">
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>4</ServiceCount>
            <Card
              title={"Framework stability Analysis"}
              description={
                "Our Team looks at how well your current system is working and if it can grow with your needs. We identify any potential risks and their impact, ensuring everything runs smoothly when we put our maintenance plans in place."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>5</ServiceCount>
            <Card
              title={"Implementation and testing"}
              description={
                "Once the maintenance plan is complete, we put it into action and evaluate it in a safe environment. Our Testing makes sure everything works as expected and solves the issues we identified, so you can be confident it is dependable and effective."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>6</ServiceCount>
            <Card
              title={"Real-Time System Monitoring"}
              description={
                "After the Maintenance plan is live, we keep a close on your systems to spot and fix any issues before they become problems. Our real-time monitoring helps avoid downtime and keeps everything running smoothly."
              }
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center">
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>7</ServiceCount>
            <Card
              title={"Incident management"}
              description={
                "If Something unexpected happens, our team quickly steps in to figure out what went wrong. We then apply the best solution to fix the issue and work to minimize any disruption to your business, so things keep running smoothly."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>8</ServiceCount>
            <Card
              title={"Feedback and reporting"}
              description={
                "We provide clear reports on what we have done and how your system is performing. These reports are useful for keeping records, planning, and helping us keep improving our services."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-1 my-1 md:my-3">
            <ServiceCount>9</ServiceCount>
            <Card
              title={" Ongoing Preventive Maintenance"}
              description={
                "Our team Keeps taking steps to prevent issues based on past experiences and your feedback, making sure your software stays up- to- date and ready for the future."
              }
            />
          </div>
        </div>
        <div className="block w-[90%] md:max-w-[70%] mx-auto my-2 md:my-5 p-2 md:p-4 bg-[#350668] rounded shadow">
          <p className="text-center text-white text-base md:text-xl">
            By following this process, we make sure that your software, systems, and applications stay running smoothly, securely, and ready for the future.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Process;
