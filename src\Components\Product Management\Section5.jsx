import React from "react";

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:w-[512px] h-auto md:h-[300px] p-3 md:p-5 bg-white border text-justify rounded-md shadow-md overflow-hidden">
      <h2 className="text-[#7716BC] text-base md:text-lg font-semibold mb-1 md:mb-2">
        {title}
      </h2>
      <p className="text-sm md:text-base" dangerouslySetInnerHTML={{
            __html: description,
          }}></p>
    </div>
  );
};

const Section5 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Why Choose Our{" "}
        <span className="text-[#F245A1]">
          Product  Management Consulting
        </span>{" "}
        Services
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Tailored Solutions According to Your Business"}
            description={
              "All businesses and offerings are different. It is for this reason that we focus on providing tailored product management services focused on addressing your needs and goals. Starting from building new products as a startup, to portfolio management for an existing company, we have the experience to aid you with all requirements."
            }
          />
          <Card
            title={"End-to-End Support"}
            description={
              "From strategy creation, through its implementation, and in every stage of the product life cycle, Valueans is willing to help you. The mix of consulting, technical development, and post-launch services guarantee that every product achieves sustainable success. The systematic application of Valueans enables entrepreneurs to improve their products for enhanced competitiveness in the industries."
            }
          />
        </div>

        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Expertise in diverse Industries"}
            description={
              "Valueans cherishes the uniqueness of different professions which our employees have built their expertise in such as <a href='/services/saas-application-development' class='text-[#7716BC] hover:underline'>SaaS</a>, <a href='/services/enterprise-mobile-app-development' class='text-[#7716BC] hover:underline'>app development</a>, or even <a href='/services/fintech-software-development' class='text-[#7716BC] hover:underline'>Fintech</a>. This enables us to offer intelligence and solutions that cut across multiple industries. You can be assured that we have the skills and know-how needed to assist you in the development of financial applications, SaaS products, or even mobile applications for the general populace."
            }
          />
          <Card
            title={"Agile & Scalable Solutions"}
            description={
              "Since our Product Management Services are modular, they can serve your business as it expands. We use agile approaches which serve to make your product development fast and effective while responding to changes in the market. In a nutshell, our solutions are both agile and scalable to fit your needs."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section5;
