import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl border h-auto md:h-[230px] border-pink-500  p-2 md:p-4 rounded-md shadow-md">
      <div className="flex items-center gap-2">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32}
            height={32}
            className="w-full h-full"
          />
        </div>
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <p
        className="text-sm md:text-base text-justify ml-8"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="container ">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          <span className="text-[#F245A1]">Advance Technologies </span>
          To Pair With Your App
        </h2>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-6 md:my-[42px] ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"IoT"}
              description={
                "Mobile application development will probably be a component of your strategy if you intend to introduce an Internet of Things product or any linked devices. We'll assist you in providing a productive mobile solution that allows you to monitor critical metrics, manage the <a href='/technologies/iot-deployment-technologies' class='text-[#7716BC] hover:underline'> IoT</a> ecosystem, and gain insightful knowledge."
              }
            />
            <InfoCard
              title={"Block Chain"}
              description={
                "If you want to raise the level of data privacy and make the app safer, you may use blockchain development for your project. Furthermore, implementing blockchain technology improves financial transaction efficiency and transparency."
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"AI & ML"}
              description={
                "Incorporate <a href='/technologies/ai-ml-solutions' class='text-[#7716BC] hover:underline'> machine learning and artificial intelligence</a> into your product to improve decision-making, automate laborious processes, and evaluate key data. As a reputable supplier of AI software development services, we will assist your mobile application in producing measurable results."
              }
            />
            <InfoCard
              title={"Cloud Computing "}
              description={
                "You'll have lots of chances to strengthen your mobile application development project with cloud consulting services. By embracing cloud computing tools, you can support API integrations between multiple sources, drive data-intensive processes, and facilitate rapid delivery. "
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
