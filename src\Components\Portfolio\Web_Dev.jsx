import { HelpCircleIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import ProjectsRow from "./ProjectsRow";

const Portfolio_Featured = () => {
  return (
    <section className="">
      <ProjectsRow
        image={"/Images/PaintReady.png"}
        Heading={"Paint Ready"}
        paragrph={
          "At Valueans, our team of dedicated developers has successfully delivered enterprise software development solutions to many clients.  One of the most prominent projects is Paint Ready which was designed......"
        }
        link={"/portfolio/paintready"}
      />
      <ProjectsRow
        image={"/Images/Ekoj.webp"}
        Heading={"EKOJ"}
        paragrph={
          "Our client required us to develop an e-commerce SaaS product for selling medicines online. The major purpose of this SaaS..."
        }
        link={"/portfolio/ekoj"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/AlcoholMonitor.webp"}
        Heading={"Ohio Alcohol Monitoring Systems"}
        paragrph={
          "Valueans specializes in developing software solutions that aim to boost business profitability by streamlining operations and improving efficiency. Recently we developed Ohio Alcohol Monitoring Systems ....."
        }
        link={"/portfolio/ohio-alcohol-monitoring-systems"}
      />
      <ProjectsRow
        image={"/Images/IMS.webp"}
        Heading={"IMS Management System"}
        paragrph={
          "IMS, a Philippines-based inventory management system designed to help businesses streamline their inventory processes. This production is design for company name IMS internal use.We provided easy-to-use...."
        }
        link={"/portfolio/ims-management-system"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/healthCarep.webp"}
        Heading={"Health Care System"}
        paragrph={
          "The Health Care System is one of our most successful projects that has helped a hospital improve its management and operations. Due to the nature of the healthcare industry, we had to be very particular...."
        }
        link={"/portfolio/health-care-system"}
      />
      <ProjectsRow
        image={"/Images/BolehLand.webp"}
        Heading={"BOLEHLAND"}
        paragrph={
          "One of our clients had a unique business idea where they wanted to make lending money easier than ever. They wanted us to develop a FinTech application where they could easily get a loan within......"
        }
        link={"/portfolio/bolehland"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/Akatsuki.webp"}
        Heading={"Akatsuki-opvdata"}
        paragrph={
          "A fintech project, which collects data for trading and creates graphs to help investors make informed decisions. Our platform utilizes advanced data analytics and machine learning algorithms to collect, ......"
        }
        link={"/portfolio/akatsuki-opvdata"}
      />
      <ProjectsRow
        image={"/Images/Enbar.webp"}
        Heading={"Enbarr"}
        paragrph={
          "At Valueans, we aim to deliver unique digital solutions designed to meet clients’ specific business needs. Our solution brings communities together. One innovative example is Enbarr, which is an online ...."
        }
        link={"/portfolio/enbarr"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/GTRLoan.png"}
        Heading={"GTR LOAN"}
        paragrph={
          "Another FinTech product created by Valueans is GTR LOAN, which provides a simple and rapid application procedure for borrowing money. Users can get money using this app without having to go through ..."
        }
        link={"/portfolio/gtr-loan"}
      />
      <ProjectsRow
        image={"/Images/Rafiq.png"}
        Heading={"RAFIQ"}
        paragrph={
          "Valueans has provided digital solutions to several industries. One of the latest projects we delivered is RAFIQ which is an Artificial Intelligence solution. The main purpose of developing RAFIQ was to bring...."
        }
        link={"/portfolio/rafiq"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/JobWorth.webp"}
        Heading={"JOBWORTH"}
        paragrph={
          "Our clients wanted a platform where job seekers and employers could connect. They asked us to deliver a solution where both parties can create their profiles and reach out to each other....."
        }
        link={"/portfolio/jobworth"}
      />
      <ProjectsRow
        image={"/Images/HBS.webp"}
        Heading={"HealthCare Billing System"}
        paragrph={
          "Valueans has provided digital solutions to several businesses helping them to streamline their operations and processes that result in their growth. We’ve dealt with many industries and healthcare is ......"
        }
        link={"/portfolio/healthcare-billing-system"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/DS.webp"}
        Heading={"Deployment System"}
        paragrph={
          "Valueans designed a captivating UI/UX for a robust app management platform for one of its clients. The major goal behind developing this platform was to streamline the entire lifecycle of development. ...."
        }
        link={"/portfolio/deployment-system"}
      />
      <ProjectsRow
        image={"/Images/HRM.webp"}
        Heading={"HRM"}
        paragrph={
          "At Valueans, we aim to deliver unique digital solutions designed to meet clients’ specific business needs. Our solution brings communities together. One innovative example is Enbarr, which is an online ...."
        }
        link={"/portfolio/HRM"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/Semantic_fitness.png"}
        Heading={"Somatic Fitness App"}
        paragrph={
          "At Valuenas, we love to deliver user-friendly mobile solutions that can make a difference. We recently worked on Somatics Fitness & Nutrition (SFN) software. It’s a smartphone application to help..."
        }
        link={"/portfolio/somatic-fitness-app"}
      />
    </section>
  );
};

export default Portfolio_Featured;
