import React from "react";

const Section6 = () => {
  return (
    <section className=" mb-10 md:mb-24 p-4 md:p-8">
      <div className="container flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Why Choose{" "}
            <span className="text-[#F245A1]">SaaS Application Development</span>{" "}
            Services from Valueans? 
          </h2>
          <p className="text-base md:text-xl">
            Choosing valueans for SaaS app development services is more than choosing a service. You are gaining a partner dedicated to driving your business growth. Here’s why partnering with valueans is a smart choice:
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <div className="p-5 flex flex-col gap-3 md:gap-5 text-justify">
              <div className="flex items-start gap-4 text">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Once you’re associated with us you can expect your business and systems to grow exponentially. An optimized architecture that can handle the peaks of performance and handle growing audiences.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  A service that covers everything. From the initial thoughts to the final launch. You will not even have to think about another vendor.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  We want to <a href='/services/product-management-consulting' class='text-[#7716BC] hover:underline'>  manage your products</a> in a way so they are far reaching. Anyone with an internet connection will be able to access your apps.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Agile practices are at our core. It enables us to adapt quickly to changing requirements. Also reduces the amount of time taken to launch a product. It is a way to conduct more iterations in less time for a refined outcome.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg ">
                  We are concerned about your security just as much as you are, if not more. Our security is tight and GDPR and HIPAA standards are religiously followed among others.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section6;
