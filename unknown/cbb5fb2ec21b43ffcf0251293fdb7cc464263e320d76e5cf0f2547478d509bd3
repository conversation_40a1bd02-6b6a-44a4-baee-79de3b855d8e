import React from 'react'
import Image from 'next/image';
const InfoCard = ({ title, description }) => {
    
  return (
    <div className="w-full md:max-w-lg flex items-start gap-1  p-2  ">
      <div className="w-[20px] h-[20px] shrink-0">
        <Image
          src="/Images/Tick.png"
          alt="arrow"
          width={50}
          height={50}
          className="object-contain"
        />
      </div>
      <div>
        <h3
          className="font-semibold  text-lg md:text-xl"
          dangerouslySetInnerHTML={{ __html: title }}
        />
        <p
          className="text-sm md:text-base text-justify"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      </div>
    </div>
  );
};
const aRvRCardData = [
    {
      title: "Do You Have Teams That Are Disconnected?",
      description:
        "That is something we can resolve. ReOps helps your team create a profitable, consistent experience that boosts your bottom line by developing procedures for every stage of the customer journey, from the initial touch to the five-star review.",
    },
    {
      title: "Do You Have Untidy Tech and Data Stacks?",
      description:
        "That is something we can resolve. Having a single tool to manage every bit of that data is essential to ReOps. This is no longer an issue thanks to easy-to-use technology that provides quick data analysis and insight.",
    },
    {
      title: "Do You Have Uncertain Revenue Streams?",
      description:
        "We have a decade of experience that is something we can resolve. Perhaps the greatest advantage of ReOps is knowing precisely where and why income is flowing allows you to choose which levers to pull in order to achieve the strategy",
    },
    
  ];
const HomeP9 = () => {
  return (
       <div className="container px-4 mb-10 md:mb-24 md:py-10 md:px-[75px] py-5">
          <h2 className="text-2xl md:text-3xl font-semibold text-center mb-3">
            Growth That Is Serious and Sustainable Offering! <span className="text-[#F245A1]"></span>
          </h2>
          <p className="text-lg md:text-xl md:w-[70%] mx-auto text-center">We don’t just deliver leads, we also position you for success, since expanding too quickly might be risky. All aspects of marketing, sales, and service are included in a Valueans ReOps strategy, which creates procedures to increase productivity and scale. </p>
    
          <div className="flex flex-col-reverse md:flex-row justify-center items-stretch mt-6 md:mt-[42px]">
            <div className="w-full md:w-[55%]">
              <div className="grid grid-cols-1 gap-4">
                {aRvRCardData.map((card, index) => (
                  <InfoCard
                    key={index}
                    title={card.title}
                    description={card.description}
                  />
                ))}
              </div>
            </div>
            <div className="relative w-full md:w-[45%] min-h-[250px] md:min-h-0">
              {/* Use fill + object-cover to cover the entire container */}
              <Image src="/Images/Homep9.jpg" alt="AI" fill className="object-cover" />
            </div>
          </div>
        </div>
  )
}

export default HomeP9