import React from "react";
import Image from "next/image";

const HomeP2 = () => {
  return (
    <section className="container ">
      <div className="text-center font-poppins rounded-t-3xl bg-[#F6EBF6]  py-2 md:py-8 px-4  mt-4 md:mt-8 mb-10 md:mb-24">
        <h2 className="text-xl font-semibold md:text-2xl text-[#232536]">
          <span className="text-3xl md:text-[42px] text-[#8B4AD1] mr-2">
            80%
          </span>
          of Your Solution is Already Engineered.
        </h2>
        <p className="text-base md:text-lg  text-[#232222]   mt-1 md:mt-3">
          Why build from scratch when the foundation of your application is
          already designed, tested, and ready for deployment? We don't waste
          your budget on solved problems. Our revolutionary ReOps framework
          utilizes a vast library of secure, enterprise-grade code so we can
          focus on one thing: delivering your unique vision to the market,
          faster and more effectively than ever before.
        </p>
      </div>
      <section>
        <div className="  container  px-4 py-8 font-sans">
          <div className="flex flex-col md:flex-row justify-between items-center gap-8 mb-12">
            <div className="flex-1 font-poppins">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-semibold mb-3 md:mb-6 ">
                ReOps: The Framework for High-Velocity Development
              </h2>
              <p className="mb-1 md:mb-4 text-justify text-[#232222] text-sm md:text-lg">
                In today's market, speed is non-negotiable. With its endless
                cycles of repetitive work, traditional development is a
                liability. ReOps (Reuse Operations) is our proprietary framework
                designed to eliminate this liability.
              </p>
              <p className="mb-1 md:mb-4 text-justify text-[#232222] text-sm md:text-lg">
                It combines a vast library of pre-engineered, enterprise-grade
                code modules with a disciplined operational process. Instead of
                starting from scratch, we begin at 80% complete, allowing us to
                focus entirely on your unique business logic and user
                experience. The result is a radical acceleration in your
                time-to-market without ever compromising on quality or
                scalability.
              </p>
            </div>
            <div className="flex flex-1 justify-end items-center relative  mt-8 md:mt-0">
              <Image
                src="/Images/bulb.svg"
                alt="Innovative ideas"
                width={264}
                height={247}
                className="w-[264px] h-[247px] md:w-[393px] md:h-[367px]"
              />
            </div>
          </div>

          <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center gap-3 md:gap-8 mb-12">
            <div className="flex-1">
              <Image
                src="/Images/dev_reuse.svg"
                alt="AI and DevOps"
                width={282}
                height={266}
                className="w-[282px] h-[266px] md:w-[395px] md:h-[373px]"
              />
            </div>
            <div className="flex-1 text-justify font-poppins">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-semibold mb-3 md:mb-6">
                The Intelligence Behind the Speed
              </h2>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                Our methodology is powered by an intelligent AI core that acts
                as your project's master architect. It doesn't just find code;
                it analyzes your specific business needs to select and assemble
                the optimal components, ensuring a flawless and highly efficient
                foundation. 
              </p>
              {/* <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                ReOps integrates with established DevOps practices, enhancing
                them without disruption. It utilizes current infrastructure and
                workflows, ensuring that DevOps practitioners transition
                naturally and gradually.
              </p>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                Utilizing the power of AI, we make sure your specifications
                align perfectly with what we have in store. We combine human
                creativity and AI capabilities to deliver what's best for you.
                Because at Valueans, we don't just code, we create-a thoughtful,
                durable, and reliable solution tailored to your needs.
              </p> */}
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-between items-center gap-8 mb-12">
            <div className="flex-1 mb-12 text-justify font-poppins">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-semibold mb-3 md:mb-6 ">
                The ReOps Advantage: More Than Just Speed
              </h2>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                By building on a proven foundation, you gain critical advantages
                that give you a definitive edge in the market.
              </p>
              <ul className="text-[#232222] text-sm md:text-lg list-disc ml-4">
                <li>
                  Unmatched Velocity: Launch products and critical features in a
                  fraction of the traditional time. This allows you to capture
                  market opportunities and respond to user feedback with
                  unprecedented agility.
                </li>
                <li>
                  Rock-Solid Reliability: Build upon code that has been tested
                  and hardened across numerous production environments. This
                  dramatically reduces risk and ensures a stable,
                  high-performance user experience.
                </li>
                <li>
                  Intelligent Scalability: Your application is built using a
                  modern, modular architecture. This provides the flexibility to
                  adapt to future challenges and the power to scale effortlessly
                  as your user base grows.
                </li>
                <li>
                  Strategic Investment: By eliminating redundant work, your
                  budget is focused directly on innovation and the custom
                  features that create your competitive edge. It's a smarter
                  allocation of your most critical resources.
                </li>
              </ul>
            </div>

            <div className="flex flex-1 justify-end">
              <Image
                src="/Images/accelate.svg"
                alt="Accelerate development"
                width={400}
                height={300}
                className="w-full max-w-[400px] h-auto object-contain"
              />
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default HomeP2;
