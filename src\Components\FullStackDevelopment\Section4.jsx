import React from "react";

const Section4 = () => {
  return (
    <section className="w-[95%] md:w-[60%] mx-auto p-8 border border-[#F245A1] rounded-lg my-10 md:my-20">
      <h3 className="text-base md:text-xl font-semibold">
        With full stack development services at Valueans, you can:
      </h3>
      <div className="py-5 flex flex-col text-sm md:text-base gap-3 md:gap-5">
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Create responsive and dynamic web apps.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className=""> Create both back-end and front-end features.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
            {" "}
            Provide software that is secure, scalable, and maintained.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Easily connect databases and user interfaces.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
            Assure seamless client-side and server-side application
            connectivity.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
            Automates deployment and testing to guarantee dependable upgrades.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
            Provide users with a smooth <a href='/technologies/cross-platform-mobile-app-development-services' class='text-[#7716BC] hover:underline'> cross-platform experience.</a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default Section4;
