import React from "react";
import Card from "./Card";

const newDataCards = [
  {
    title: "Retail Optimization",
    points: [
      "Use predictive analytics to forecast demand and optimize inventory.",
      "Enhance customer experience through personalized recommendations.",
      "Find trends to inform product development and marketing strategies.",
    ],
  },
  {
    title: "Healthcare Insights",
    points: [
      "Analyze patient data to improve outcomes and streamline operations.",
      "Ensure compliance with regulations through robust governance.",
      "Utilize <a href='/services/ai-business-solutions' class='text-[#7716BC] hover:underline'>AI business solutions</a> to enhance diagnostic accuracy and treatment planning.",
    ],
  },
  {
    title: "Financial Services",
    points: [
      "Detect fraudulent activities using advanced machine learning models.",
      "Refine portfolio management through predictive analytics.",
      "Streamline reporting process to meet regulatory requirements.",
    ],
  },
  {
    title: "Manufacturing Efficiency",
    points: [
      "Leverage data to check equipment performance and reduce downtime.",
      "Implement predictive maintenance to avoid costly failures.",
      "Improve supply chain operations through data-driven insights.",
    ],
  },
];

const RealWorldSection = () => {
  return (
    <div className="container my-10 md:my-24 p-4 md:p-6">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        Real World Applications of{" "}
        <span className="text-[#7716BC]">Data Analytics as a Service</span>
      </h2>
      <p className="text-base md:text-xl text-center my-2">
        Data analytics service is not just about crunching numbers—it is about
        solving real business problems and unlocking opportunities. <br />
        <span className="font-bold">
          Here are examples of how our services create value:
        </span>
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {newDataCards.map((data, index) => (
          <Card key={index} title={data.title} content={data.points} />
        ))}
      </div>
    </div>
  );
};

export default RealWorldSection;
