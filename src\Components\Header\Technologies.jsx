"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const Technologies = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/tech-nav.jpeg"} width={400} height={400} alt="Technologies" />
        </div>

        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          {/* Column 1 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/technologies/pwa-development-technologies"
              onClick={(e) => handleEnhancedNavigation("/technologies/pwa-development-technologies", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/pwa-development-technologies", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Progressive Web Apps
            </Link>
            <Link
              href="/technologies/nlp-solutions"
              onClick={(e) => handleEnhancedNavigation("/technologies/nlp-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/nlp-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              NLP
            </Link>
            <Link
              href="/technologies/low-code-automation"
              onClick={(e) => handleEnhancedNavigation("/technologies/low-code-automation", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/low-code-automation", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Low Code Deployment
            </Link>
          </div>

          {/* Column 2 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/technologies/ar-vr-development-technologies"
              onClick={(e) => handleEnhancedNavigation("/technologies/ar-vr-development-technologies", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/ar-vr-development-technologies", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              AR/VR
            </Link>
            <Link
              href="/technologies/microservices-consulting"
              onClick={(e) => handleEnhancedNavigation("/technologies/microservices-consulting", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/microservices-consulting", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Microservices
            </Link>
            <Link
              href="/technologies/predictive-analytics-technologies"
              onClick={(e) => handleEnhancedNavigation("/technologies/predictive-analytics-technologies", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/predictive-analytics-technologies", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Predictive Analytics
            </Link>
          </div>

          {/* Column 3 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/technologies/iot-deployment-technologies"
              onClick={(e) => handleEnhancedNavigation("/technologies/iot-deployment-technologies", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/iot-deployment-technologies", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              IoT
            </Link>
            <Link
              href="/technologies/ai-ml-solutions"
              onClick={(e) => handleEnhancedNavigation("/technologies/ai-ml-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/ai-ml-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              AI/ML
            </Link>
            <Link
              href="/technologies/cross-platform-mobile-app-development-services"
              onClick={(e) => handleEnhancedNavigation("/technologies/cross-platform-mobile-app-development-services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/technologies/cross-platform-mobile-app-development-services", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Cross-platform and Hybrid Development
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Technologies;
