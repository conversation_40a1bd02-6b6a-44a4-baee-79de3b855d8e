import React from "react";
import HorizontalColoredCard from "../PWA_development/HorizontalColoredCard";

const Section8 = () => {
  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        <span className="text-[#F245A1]">Case Studies</span> and Success Stories
      </h2>
      <div className="items-center gap-3 md:gap-6 my-[32px] mx-[20px] ">
        <div className="bg-[#F245A126] flex flex-col gap-3 md:flex-row md:justify-between p-4 md:p-8">
          <HorizontalColoredCard
            heading={"Manufacturing: Improving Efficiency and Maintenance"}
            paragraph={
              "Expenses, a worldwide manufacturing corporation dealt with costly downtimes due to equipment failures. Valueans implemented an IOT-powered Predictive maintenance system with smart sensors and analytics.  The routine checkups and monitoring of machinery health reduced machine downtime by 30%, which resulted in heightened production efficiency and reduced maintenance expenses. "
            }
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
          <HorizontalColoredCard
            heading={"Healthcare: Monitoring Patients Through IOT Technology"}
            paragraph={
              "A <a href='/services/healthcare-software-development' class='text-[#7716BC] hover:underline'>healthcare provider</a>  was seeking a solution that helps track vitals and alerts doctors instantly. Valueans created a secure IOT platform powered by wearable devices to analyze and collect data increasing <a href='/technologies/ai-ml-solutions' class='text-[#7716BC] hover:underline'>real-time health insights</a>. The healthcare provider experienced enhanced care quality, a 25% decrease in hospital readmissions, and ensured regulatory compliance from patients using the platform. "
            }
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
        <div className="flex flex-col gap-3 md:flex-row md:justify-between  items-center mt-5 bg-[#794CEC26] p-4 md:p-8">
          <HorizontalColoredCard
            heading={
              "Retail: Automating Inventory Management and Revenue Growth"
            }
            paragraph={
              "A retail chain could not balance inventory, leading to overstocking and shortage, which negatively impacted profit margins. Valueans tried IOT-based inventory sensors connected to a cloud-driven analytics system. The retailer experienced improved supply management having the ability to forecast demand accurately, which led to more optimal waste management and inventory balance achieving heightened revenue."
            }
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
          <HorizontalColoredCard
            heading={"Smart Cities: Effective Control of Traffic Flow"}
            paragraph={
              "Challenge: The city experienced heavy traffic issues along with poor public transport service. Solution: Valueans implemented an IOT-enabled traffic control system with a network of sensors working together with AI-based analytics that work to smoothly regulate traffic. Outcome: The city congestion decreased by 20 percent, and transit effectiveness and user satisfaction improve "
            }
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
      </div>
    </div>
  );
};

export default Section8;
