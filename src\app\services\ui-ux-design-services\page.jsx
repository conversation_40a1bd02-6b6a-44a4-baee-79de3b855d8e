import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/AI/Section2";
import Card from "@/Components//App Integration/Card_2";
import Faq from "@/Components/Faq/UiUxFaq";
import HomeP8 from "@/Components/Homepage/HomeP8";
import CardCarousel from "@/Components/UI_UX/CardCarousel";
import Process from "@/Components/UI_UX/process";
import Image from "next/image";
import ServiceCard_2 from "@/Components/Card/ServiceCard_2";

export const metadata = {
  title: "Expert UX/UI Design Solution for Mobile & Websites",
  description:
    "Reduce bounce rate with ux/ui design solutions. Striking visuals and the ease of use are the cornerstones of our user interface design services.",
  alternates: {
    canonical: "https://valueans.com/services/ui-ux-design-services",
  },
  openGraph: {
    title: "Expert UX/UI Design Solution for Mobile & Websites",
    description: "Reduce bounce rate with ux/ui design solutions. Striking visuals and the ease of use are the cornerstones of our user interface design services.",
    url: "https://valueans.com/services/ui-ux-design-services",
    type: "website",
  },
};
const page = () => {
  const UiUxcardData = [
    {
      title: "User-Centered Designs",
      content:
        "Our UI UX design services start and end with users. Our process involves conducting extensive user research to understand their needs, behaviors, and pain points. Our designs are intuitively functional and up-to-date. ",
    },
    {
      title: "Expert Team of Designers",
      content:
        "We have seasoned UI/UX designers with years of experience across various industries. Whether you belong to healthcare, travel, or fintech, our experts have designs that would suit your brand and industry.",
    },
    {
      title: "Comprehensive Services",
      content:
        "We offer a full spectrum of UI/UX design services. Our end-to-end design services guarantee the highest quality product for you and your users.",
    },
    {
      title: "Ongoing Support and Maintenance",
      content:
        "We won't abandon you after launching. We offer <a href='/services/it-maintenance-support-services' class='text-[#7716BC] hover:underline'>ongoing support and maintenance</a> to make sure that your product is compatible with the latest technologies and trends.",
    },
    {
      title: "Collaborative Process",
      content:
        "At Valueans, we have a collaborative nature while developing a product. We work closely with our developers in the experiment phase to ensure that design and development go hand in hand. We ask for your feedback at every step to implement necessary changes and enhancements. ",
    },
  ];

  const UiUxTech = [
    {
      title: "Languages",
      content: ["HTML", "CSS", "Bootstrap", "jQuery"],
    },
    {
      title: "Wireframes & Prototypes",
      content: ["Balsamiq", "InVision", "Adobe XD", "Sketch"],
    },
    {
      title: "Visual Design",
      content: ["Figma", "Photoshop", "Illustrator", "InDesign"],
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"Images/UiUx-bg.jpeg"}
        heading={"UI/UX Design Services"}
        bannerText={
          "We turn your concepts into interactive designs that demonstrate the user's path, arrangement, and essential features."
        }
      />
      <Section2
        lefttext={
          "Our UI/UX design and development services are not only visually pleasing but also combine functionality, automation, and creativity. We focus on innovation and user-centric designs. Partner with Valueans and get user interface design services that allow consumers to have a breezy experience. "
        }
        righttext={
          "Our years of experience in this field make us one of the top UI/UX design companies in the US. Unlike others, we stay updated on all trends and technologies, so you always stand out in the market."
        }
        fontClasses={"font-normal text-base md:text-lg"}
      />

      <section className="my-10 md:my-24  container">
        <h2 className="text-center text-2xl md:text-3xl md:leading-[40px] font-semibold mb-2 md:mb-5 ">
          Why Choose Us?
        </h2>
        <p className="text-center text-base md:text-xl capitalize mb-2 md:mb-5">
          There are hundreds of companies out there that offer the same UI/UX
          design services as us,
          <br /> so how are we different?{" "}
          <span className="font-bold">Keep reading:</span>
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
          {UiUxcardData.map((card, index) => (
            <ServiceCard_2
              key={index}
              title={card.title}
              content={card.content}
            />
          ))}
        </div>
      </section>
      <HomeP8
        heading={"Start a conversation with us today"}
        paragrapgh={
          "Interested to know more about our UI/UX development services but confused about how to get started? Contact us now and we’ll help you to understand what’s best for your business. Our team is ready to be your partner throughout the process.  "
        }
        buttonText={"Connect with us"}
        to={"/contact"}
      />
      <section className="mt-10 md:mt-24 ">
        <h2 className="text-2xl md:text-3xl md:leading-[40px] font-semibold text-center mb-4 md:mb-10">
          What We <span className="text-[#F245A1]">Offer</span>
        </h2>

        <CardCarousel />
      </section>
      <section className="my-10 md:my-24 container">
        <h2 className="text-2xl md:text-3xl md:leading-[40px] font-semibold text-center">
          UI/UX Technology at <span className="text-[#F245A1]">Valueans</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 mt-5 gap-6">
          {UiUxTech.map((item, index) => (
            <div key={index} className="">
              <div className="justify-items-center">
                <Card title={item.title} content={item.content} />
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className="py-5 md:py-10 my-10 md:my-24 bg-blue-100">
        <div className="container flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col gap-10">
            <div className="block w-full  p-6 bg-pink-100 border border-purple-500 rounded-lg shadow overflow-hidden">
              <h3 className="text-[#7716BC] text-base md:text-2xl font-semibold">
                Challenges Our UI/UX Designs Can Handle
              </h3>
              <ul
                style={{ listStyleType: "disc", listStylePosition: "inside" }}
                className="list-disc list-inside"
              >
                <li>Complex user journeys</li>
                <li>Cross-platform inconsistency</li>
                <li>Insufficient Productivityr</li>
                <li>Outdated design solutions</li>
                <li> Low user-engagement </li>
                <li> Usability Issues</li>
              </ul>
            </div>
            <div className="block w-full  p-6 bg-purple-100 border border-purple-500 rounded-lg shadow overflow-hidden">
              <h3 className="text-[#7716BC] text-base md:text-2xl font-semibold">
                Features of Our UI/UX Design Services
              </h3>
              <ul
                style={{ listStyleType: "disc", listStylePosition: "inside" }}
                className="list-disc list-inside"
              >
                <li>AR Experience Design</li>
                <li>
                  <a
                    href="/technologies/nlp-solutions"
                    className="text-[#7716BC] hover:underline"
                  >
                    {" "}
                    Voice Interface Design (powered by NLP)
                  </a>
                </li>
                <li>Custom Iconography and visual elements</li>
                <li>Mobile app UI and UX design services</li>
                <li>Responsive designs</li>
                <li>UI and UX consulting</li>
                <li>Data-driven designs</li>
                <li>Design workshops</li>
                <li>
                  <a
                    href="/services/custom-web-development-services"
                    className="text-[#7716BC] hover:underline"
                  >
                    {" "}
                    Web design services
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div>
            <Image
              src={"/Images/uiux_frame.png"}
              alt="UI Ux"
              width={500}
              height={500}
            />
          </div>
        </div>
      </section>
      <Process />
      <Faq />
    </div>
  );
};

export default page;
