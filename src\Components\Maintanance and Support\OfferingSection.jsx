import React from "react";
import Image from "next/image";
import Card from "./OfferCard";

const OfferingSection = () => {
  const cardsData = [
    {
      title: "Application Performance Management",
      description:
        "With our ongoing support and maintenance approach, we monitor and optimize your applicant’s performance to ensure smooth operations, fast performance, and create user experience. Here’s how we manage your application’s performance: ",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "We track your application’s performance to quickly spot any issues or slowdowns. This helps us to identify problems before they affect Make sure that your app stays secure and works properly.",
        "We work to improve your app’s efficiency, making sure it uses resources effectively and runs best.",
        "We make sure that your application responds quickly and processes requests fast, which is important for keeping users engaged.",
        "We focus on providing an enjoyable experience for users by making sure that apps are working smoothly and meet with user needs without any glitches.",
        "We make sure that your application stays stable by minimizing downtime and avoiding crashes, ensuring it is always accessible and working reliably for your users.",
      ],
    },
    {
      title: "Error Tracking and Debugging",
      description:
        "Our IT maintenance company ensures your system runs smoothly through continuous monitoring. Here’s how we handle issues or bugs effectively:",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "We monitor your system to identify any issues or bugs as soon as possible.",
        "All detected issues are documented for further analysis and tracking.",
        "We analyse the root cause of the problem to understand its impact and prevent future occurrences.",
        "Our team resolves the issue to restore the system’s functionality and minimize downtime.",
      ],
    },
    {
      title: "Application Compliance Management",
      description:
        "Our application maintenance and support services include ensuring your software meets all regulatory, legal and industry standards. Here’s how we make it happen:",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "Make sure your software follows all laws and industry rules.",
        "Keep an eye on your app to stay updated with new rules.",
        "Update your app to meet any changes in compliance requirements.",
        "Help you to avoid fines, data leaks, or legal trouble.",
        "Protect user data to keep their interest in your business.",
      ],
    },
    {
      title: "IT Infrastructure Services",
      description:
        "We provide reliable IT support and maintenance services to keep your business running smoothly and securely. Here’s how we deliver these services:",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "Manage your network to make sure smooth connectivity.",
        "Provide support to keep your services running efficiently.",
        "Offer cloud services for flexibility and scalability.",
        "Make sure your data is regularly backed up and recoverable.",
        "Protect your system and data from security threats.",
      ],
    },
    {
      title: "CI/CD DevOps Implementation",
      description:
        "As part of our maintenance and support services, we provide CI/CD DeOps solutions to make your <a href='/services/enterprise-software-development' class='text-[#7716BC] hover:underline'>software development</a> faster, smoother, and more efficient. Here’s how it works:",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "Automate the process of continuous integration and delivery for faster software updates.",
        "Improve collaboration between development and operation teams.",
        "Help speed up software development by automating key processes.",
        "Ensure a more efficient and effective development cycle.",
        "Enhance team collaboration for better communication and faster problem-solving.",
      ],
    },
    {
      title: "Cloud Migration",
      description:
        "Our web and <a href='/services/enterprise-mobile-app-development' class='text-[#7716BC] hover:underline'> mobile application development</a> and maintenance expertise includes providing <a href='/services/managed-cloud-services' class='text-[#7716BC] hover:underline'> cloud migration services</a> to help you securely move your data and applications to the cloud.",
      imgSrc: "/Images/service_frame.png",
      imgAlt: "Tick",
      points: [
        "Securely move your data and applications to the cloud.",
        "Ongoing support to maintain optimal cloud performance.",
        "We ensure your cloud environment runs smoothly and securely.",
      ],
    },
  ];

  return (
    <div className="grid grid-cols-1 justify-items-center items-center sm:grid-cols-2 gap-6 mt-8">
      {cardsData.map((card, index) => (
        <Card
          key={index}
          title={card.title}
          description={card.description}
          imgSrc={card.imgSrc}
          imgAlt={card.imgAlt}
          points={card.points}
        />
      ))}
    </div>
  );
};

export default OfferingSection;
