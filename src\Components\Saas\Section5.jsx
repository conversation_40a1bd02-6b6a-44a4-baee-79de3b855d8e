import React from "react";
import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl border border-pink-500  p-2 md:p-4 rounded-md shadow">
      <div className="flex items-center gap-2">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32} 
            height={32}
            className="w-full h-full"
          />
        </div>
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <p
        className="text-sm md:text-base text-justify md:ml-8"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="container">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Successful SaaS Implementation By{" "}
          <span className="text-[#F245A1]">Valueans</span>
        </h2>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6 ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"Business Process Automation"}
              description={
                "A SaaS platform created by Valueans helps provide <a href='/industries/logistics-it-solutions' class='text-[#7716BC] hover:underline'> services for logistics</a> companies to automate their delivery routing and tracking, cutting delivery times by 25%."
              }
            />
            <InfoCard
              title={"Customer Relationship Management"}
              description={
                "A CRM solution developed for a retail company improved customer Customer engagement, leading to a 40% increase in sales."
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"E-Learning Platform"}
              description={
                "Valueans Built an <a href='/industries/education-software-solutions' class='text-[#7716BC] hover:underline'> E-Learning platform</a> that provided teachers with tools for online classes, reaching 10,000 users in just Six Months. "
              }
            />
            <InfoCard
              title={"Financial Planning Tools"}
              description={
                "A SaaS financial software helps small businesses automate their budgeting and forecasting, Saving time and cutting down on mistakes."
              }
            />
          </div>
          <div className="flex justify-center">
            <InfoCard
              title={"Healthcare Management"}
              description={
                "A cloud-based SaaS app made it easiest for healthcare providers to manage patients' records and appointments, boosting patient care and streamlining administrative work."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
