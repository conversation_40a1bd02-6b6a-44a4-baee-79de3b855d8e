"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";

const Industries = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/industries-nav.jpeg"} width={400} height={400} alt="Industries" />
        </div>
        
        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/industries/healthcare-it-solutions"
              onClick={(e) => handleEnhancedNavigation("/industries/healthcare-it-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/healthcare-it-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Healthcare
            </Link>
            <Link
              href="/industries/game-design-development"
              onClick={(e) => handleEnhancedNavigation("/industries/game-design-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/game-design-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Gaming
            </Link>
            <Link
              href="/industries/ecommerce-app-development"
              onClick={(e) => handleEnhancedNavigation("/industries/ecommerce-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/ecommerce-app-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              E-commerce
            </Link>
            <Link
              href="/industries/agriculture-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/agriculture-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/agriculture-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Agriculture
            </Link>
            <Link
              href="/industries/travel-industry-solutions"
              onClick={(e) => handleEnhancedNavigation("/industries/travel-industry-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/travel-industry-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Travel
            </Link>
            <Link
              href="/industries/automotive-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/automotive-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/automotive-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Automotive
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/industries/banking-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/banking-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/banking-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Finance
            </Link>
            <Link
              href="/industries/education-software-solutions"
              onClick={(e) => handleEnhancedNavigation("/industries/education-software-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/education-software-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Education
            </Link>
            <Link
              href="/industries/enterprise-social-network"
              onClick={(e) => handleEnhancedNavigation("/industries/enterprise-social-network", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/enterprise-social-network", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Social Networking
            </Link>
            <Link
              href="/industries/oil-gas-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/oil-gas-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/oil-gas-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Oil and Gas
            </Link>
            <Link
              href="/industries/real-estate-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/real-estate-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/real-estate-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Real Estate
            </Link>
            <Link
              href="/industries/insurance-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/insurance-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/insurance-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Insurance
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/industries/telecom-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/telecom-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/telecom-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Telecom
            </Link>
            <Link
              href="/industries/construction-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/construction-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/construction-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Construction
            </Link>
            <Link
              href="/industries/manufacturing-software-development"
              onClick={(e) => handleEnhancedNavigation("/industries/manufacturing-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/manufacturing-software-development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Manufacturing
            </Link>
            <Link
              href="/industries/logistics-it-solutions"
              onClick={(e) => handleEnhancedNavigation("/industries/logistics-it-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/logistics-it-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Logistics
            </Link>
            <Link
              href="/industries/banking-technology-solutions"
              onClick={(e) => handleEnhancedNavigation("/industries/banking-technology-solutions", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/industries/banking-technology-solutions", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Banking
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Industries;
