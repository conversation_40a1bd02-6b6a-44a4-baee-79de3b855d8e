import Image from "next/image";
import React from "react";
import Heading from "../Heading/Heading";

const HomeP6 = () => {
  return (
    <div className="bg-pink-50 py-8 my-24">
      <Heading>
        <span className="text-[#F245A1]">Our Process</span>
      </Heading>
      <p className="text-base md:text-xl leading-[24px] md:leading-[57px] text-center text-[#232536]">
        The process we are working With Our client Worldwide
      </p>
      <div className="flex flex-col md:flex-row items-center md:items-start mt-8 mx-4 md:mx-[75px] space-y-8 md:space-y-0 md:space-x-4">
        {/* 1. Understanding Your Needs */}
        <div className="flex flex-col items-center">
          {/* Fixed icon container */}
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-pink-200">
            <Image
              src="/Images/bulb-icon.png"
              alt="bulb-icon"
              width={24}
              height={24}
            />
          </div>
          {/* Title & Description */}
          <p className="mt-4 text-lg font-semibold text-center text-[#232536] leading-[30px]">
            Understanding Your Needs
          </p>
          <p className="mt-2 text-base text-center text-[#232222]">
            Gather your business requirements and challenges thoroughly.
          </p>
        </div>

        {/* Line: Hidden on mobile, visible on md+ */}
        <div className="hidden md:block self-center">
          <Image src="/Images/line.png" style={{ height: "10px" }} alt="line" width={95} height={2} />
        </div>

        {/* 2. Planning and Strategy */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-pink-200">
            <Image
              src="/Images/plan-icon.png"
              alt="plan-icon"
              width={24}
              height={24}
            />
          </div>
          <p className="mt-4 text-lg font-semibold text-center text-[#232536] leading-[30px]">
            Planning and Strategy
          </p>
          <p className="mt-2 text-base text-center text-[#232222]">
            When our team has a clear grasp of your needs, it creates a detailed
            project strategy.
          </p>
        </div>

        {/* Line */}
        <div className="hidden md:block self-center">
          <Image src="/Images/line.png" style={{ height: "10px" }} alt="line" width={95} height={2} />
        </div>

        {/* 3. Design & Development Phase */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-pink-200">
            <Image
              src="/Images/dev-icon.png"
              alt="dev-icon"
              width={24}
              height={24}
            />
          </div>
          <p className="mt-4 text-lg font-semibold text-center text-[#232536] leading-[30px]">
            Design & Development Phase
          </p>
          <p className="mt-2 text-base text-center text-[#232222]">
            Designers and developers bring your project to life.
          </p>
        </div>

        {/* Line */}
        <div className="hidden md:block self-center">
          <Image src="/Images/line.png" style={{ height: "10px" }} alt="line" width={95} height={2} />
        </div>

        {/* 4. Quality Assurance */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-pink-200">
            <Image
              src="/Images/test-icon.png"
              alt="test-icon"
              width={24}
              height={24}
            />
          </div>
          <p className="mt-4 text-lg font-semibold text-center text-[#232536] leading-[30px]">
            Quality Assurance
          </p>
          <p className="mt-2 text-base text-center text-[#232222]">
            QA team ensures everything runs smoothly, meeting your high
            standards before launch.
          </p>
        </div>

        {/* Line */}
        <div className="hidden md:block self-center">
          <Image src="/Images/line.png" style={{ height: "10px" }} alt="line" width={95} height={2} />
        </div>

        {/* 5. Deployment and Support */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-pink-200">
            <Image
              src="/Images/deliver-icon.png"
              alt="deliver-icon"
              width={24}
              height={24}
            />
          </div>
          <p className="mt-4 text-lg font-semibold text-center text-[#232536] leading-[30px]">
            Deployment and Support
          </p>
          <p className="mt-2 text-base text-center text-[#232222]">
            Finally, we deploy the product and provide ongoing support for
            successful operations.
          </p>
        </div>
      </div>
    </div>
  );
};

export default HomeP6;
