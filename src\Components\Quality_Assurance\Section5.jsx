import Card_2 from "../App Integration/Card";
// const Card = ({ title, description }) => {
//   return (
//     <div className="block bg-white w-full md:max-w-sm md:h-[40vh] p-4 shadow-md rounded-xl overflow-hidden">
//       <h3 className="text-lg md:text-2xl text-[#7716BC] capitalize font-semibold mb-2 md:mb-4">
//         {title}
//       </h3>
//       <p className="text-sm md:text-base">{description}</p>
//     </div>
//   );
// };

const Section5 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        Why Choose Valueans for{" "}
        <span className="text-[#F245A1]">
          QA and <br /> Software Testing 
        </span>
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <Card_2 
          title={"Customized Testing Strategy"}
          description={
            "We start with your company objectives and develop a thorough, personalized testing plan. We select the best-fitting testing tools and the optimal strategy for your application. Additionally, we will offer input to enhance software quality and guide the testing procedure. "
          }
          height={"md:h-[350px]"}
        />
        <Card_2 
          title={"Top 1% of Talent in QA"} 
          description={
            "We are specialists in a variety of testing approaches since we are a QA development firm that only employs the best 1% of LATAM talent. Our staff members collaborate well with the <a href='/services/enterprise-software-development' class='text-[#7716BC] hover:underline'>software development</a>  team since they are proficient communicators and problem solvers."
          }
          height={"md:h-[350px]"}
        />
        <Card_2
          title={"Full Testing Services and Coverage"}
          description={
            "Our testers and QA engineers are proficient in a wide range of testing techniques, procedures, and offerings. We do usability, security, stress, and performance testing in addition to functional testing. Additionally, we use both automated and human methods to ensure high-quality software and maximum test coverage."
          }
          height={"md:h-[350px]"}
        />
      </div>
    </div>
  );
};

export default Section5;
