import ImageCard from "./ImageCard";



const Section7 = ({ImageCardData,heading, paragrapgh,spanHeading,paragraph2}) => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold mb-1">
        <span className="text-[#7716BC]">{spanHeading}</span> {heading} 
      </h2>
      <p className="md:w-[85%] md:mx-auto mb-4 text-base md:text-xl text-center">
        {paragrapgh}  
      </p>
      <div className="grid grid-cols-1  md:grid-cols-2 gap-8 justify-items-center items-center">
        {ImageCardData.map((card, index) => {
          // Check if it's the last card
          
          return (
            <div
              key={index}
             
            >
              <ImageCard
                imgsrc={card.imgsrc}
                altsrc={card.altsrc}
                title={card.title}
                description={card.description}
                cardUrl={card.cardUrl}
              />
            </div>
          );
        })}
      </div>
      <div className="   w-[70%] mx-auto  md:my-10">
        <p className={`text-center ${paragraph2 ? "px-4 py-5": ""}  text-white text-lg rounded-lg font-semibold bg-[#350668]`}>
         {paragraph2}
        </p>
      </div>
    </div>
  );
};

export default Section7;
