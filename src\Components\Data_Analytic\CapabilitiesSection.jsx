import React from "react";
import Card from "./Card";
const CapabilitiesSection = () => {
  const dataCards = [
    {
      title: "1. Data Strategy and Operating Model",
      points: [
        "Define clear aims for data initiatives.",
        "Design data operating models to streamline workflows and ensure efficiency.",
        "Align data strategies with business priorities.",
      ],
    },
    {
      title: "2. Data Management",
      points: [
        "Implement data integration, cleansing, and transformation processes.",
        "Establish secure and scalable data storage solutions.",
        "Ensure data quality and consistency across platforms.",
      ],
    },
    {
      title: "3. Analytics and Business Intelligence",
      points: [
        "Develop interactive dashboards and reports for real-time insights.",
        "Perform trend analysis to identify opportunities and risks.",
        "Enable self-service analytics to empower teams across the organization.",
      ],
    },
    {
      title: "4. Data Science and Machine Learning",
      points: [
        "Make use of <a href='/technologies/predictive-analytics-technologies' class='text-[#7716BC] hover:underline'>predictive analytics models</a> to forecast trends and outcomes.",
        "Apply machine learning algorithms to solve complex business problems.",
        "Use natural language processing and AI to unlock deeper insights.",
      ],
    },
    {
      title: "5. Data Governance",
      points: [
        "Create policies to manage data privacy and compliance.",
        "Monitor data usage and ensure adherence to regulations.",
        "Promote data literacy and ethical data practices.",
      ],
    },
    {
      title: "6. Program Management",
      points: [
        "Provide structured oversight for data initiatives.",
        "Ensure projects are delivered on time and within budget.",
        "Maintain alignment with strategic business goals.",
      ],
    },
    {
      title: "7. Enterprise Metadata Management",
      points: [
        "Develop metadata repositories to streamline data recovery.",
        "Enable collaboration by providing a unified view of data assets.",
        "Enhance decision-making with comprehensive data lineage tracking.",
      ],
    },
  ];

  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold text-center">
        Comprehensive Capabilities 
      </h2> 
      <p className="text-base md:text-xl text-center my-2 capitalize">
        Our data and analytics services are designed to address the complete data lifecycle, from acquisition to action. 
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {dataCards.map((data, index) => {
          // Conditionally apply classes for the last single card in the middle
         

          return (
            <Card
              key={index}
              title={data.title}
              content={data.points}
             // className={className}
            />
          );
        })}
      </div>
    </div>
  );
};

export default CapabilitiesSection;
